<style>
    /* Target the paragraph containing the country field */
    .wpcf7-form p:has(#country_field) {
        position: absolute !important;
        opacity: 0 !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
    }
    /* Fallback: Directly target the country dropdown */
    #country_field {
        display: none !important;
    }
    /* Set all label text to black */
    .wpcf7-form label {
        color: black !important;
    }
    
    /* Form layout styling */
    .form-row {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
        width: 100%;
    }
    .form-col {
        flex: 1;
        min-width: 0; /* Prevents flex items from overflowing */
    }
    .form-col label {
        display: block;
        margin-bottom: 5px;
    }
    .form-col select,
    .form-col input {
        width: 100%;
        height: 40px;
        border-radius: 4px;
        border: 1px solid #ddd;
        padding: 8px 12px;
        font-size: 14px;
        background-color: white;
    }
    
    /* Improved daterangepicker styling */
    #date_range {
        height: 40px;
        border-radius: 4px;
        border: 1px solid #ddd;
        padding: 8px 12px;
        width: 100%;
        font-size: 14px;
        background-color: white;
        cursor: pointer;
    }
    
    /* Submit button styling - ensure it's not white */
    .wpcf7-submit {
        background-color: #0D7EE8 !important;
        color: #ffffff !important;
        border: none !important;
        font-weight: 500 !important;
        cursor: pointer !important;
    }
    
    .wpcf7-submit:hover {
        background-color: #0A6AC7 !important;
    }
    
    /* Fixed daterangepicker styling to prevent page disruption */
    .daterangepicker {
        position: absolute !important;
        font-family: inherit;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        border: none;
        width: 320px !important; /* Narrower without ranges */
        max-width: 95vw;
        padding: 0;
        z-index: 9999 !important;
        background-color: white;
        margin-top: 5px;
        overflow: visible !important; /* Prevent scrolling */
    }
    
    /* Calendar styling */
    .daterangepicker .drp-calendar.left {
        clear: none !important;
        float: none !important;
        padding: 15px;
        width: 100%;
    }
    
    /* Hide the right calendar */
    .daterangepicker .drp-calendar.right {
        display: none !important;
    }
    
    /* Hide the ranges completely */
    .daterangepicker .ranges {
        display: none !important;
    }
    
    /* Calendar table styling */
    .daterangepicker .calendar-table {
        border-radius: 6px;
        padding: 0;
    }
    
    .daterangepicker .calendar-table table {
        width: 100%;
        border-spacing: 0;
        border-collapse: collapse;
    }
    
    .daterangepicker td, .daterangepicker th {
        width: 36px;
        height: 36px;
        text-align: center;
        vertical-align: middle;
        min-width: 36px;
        padding: 0;
        line-height: 36px;
    }
    
    .daterangepicker td.active, 
    .daterangepicker td.active:hover {
        background-color: #0D7EE8 !important;
    }
    
    .daterangepicker td.in-range {
        background-color: #E6F3FF;
        color: #333;
    }
    
    /* Button area styling */
    .daterangepicker .drp-buttons {
        padding: 15px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        clear: both;
    }
    
    .daterangepicker .drp-buttons .btn {
        border-radius: 4px;
        font-weight: 500;
        margin: 0 5px;
        padding: 8px 16px;
    }
    
    .daterangepicker .drp-buttons .btn.btn-primary {
        background-color: #0D7EE8;
        border-color: #0D7EE8;
    }
    
    .daterangepicker .drp-selected {
        font-size: 13px;
        color: #666;
        margin-right: 10px;
    }
    
    /* Responsive fixes */
    @media (max-width: 767px) {
        .form-row {
            flex-direction: column;
            gap: 10px;
        }
    }
</style>

<!-- Hidden country field -->
[country_auto* country id:country_field]

<!-- First row: State and City -->
<div class="form-row">
    <div class="form-col">
        <label>Select Your State*</label>
        [state_auto* state id:state_field]
    </div>
    <div class="form-col">
        <label>Select Your City*</label>
        [city_auto* city id:city_field]
    </div>
</div>

<!-- Second row: Billboard Location only -->
<div class="form-row">
    <div class="form-col">
        <label>Select Your Billboard Location*</label>
        <select name="Billboard-Location" required id="billboard_location_field">
            <option value="">Please select state and city first</option>
        </select>
    </div>
</div>

<!-- Hidden purpose field with default value -->
[hidden purpose value:"custom"]

<!-- Third row: Date Range -->
<div class="form-row">
    <div class="form-col">
        <label>Select Run Dates*</label>
        [text* date_range id:date_range placeholder "Click to select dates"]
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var countryField = document.getElementById('country_field');
    var stateField = document.getElementById('state_field');
    var cityField = document.getElementById('city_field');
    var dateRangeField = document.getElementById('date_range');

    // Initialize daterangepicker with single calendar
    if(dateRangeField) {
        jQuery(dateRangeField).daterangepicker({
            autoUpdateInput: false,
            minDate: new Date(),
            opens: 'center',
            showDropdowns: true,
            singleDatePicker: false,
            autoApply: false,
            linkedCalendars: false,
            locale: {
                cancelLabel: 'Clear',
                format: 'MM/DD/YYYY',
                applyLabel: 'Apply',
                daysOfWeek: ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'],
                monthNames: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
            },
            // Remove the ranges option completely
        });

        // Professional date handling with immediate localStorage saving
        jQuery(dateRangeField).on('apply.daterangepicker', function(ev, picker) {
            const dateRange = picker.startDate.format('MM/DD/YYYY') + ' - ' + picker.endDate.format('MM/DD/YYYY');
            jQuery(this).val(dateRange);

            // Immediately save to localStorage
            localStorage.setItem('runDates', dateRange);
            console.log('Date range selected and saved to localStorage:', dateRange);

            // Trigger validation check
            validateFormData();
        });

        jQuery(dateRangeField).on('cancel.daterangepicker', function() {
            jQuery(this).val('');

            // Remove from localStorage when cleared
            localStorage.removeItem('runDates');
            console.log('Date range cleared from localStorage');

            // Trigger validation check
            validateFormData();
        });
        
        // Add calendar icon to input
        jQuery(dateRangeField).css('background-image', 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'16\' height=\'16\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23666\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\'%3E%3Crect x=\'3\' y=\'4\' width=\'18\' height=\'18\' rx=\'2\' ry=\'2\'%3E%3C/rect%3E%3Cline x1=\'16\' y1=\'2\' x2=\'16\' y2=\'6\'%3E%3C/line%3E%3Cline x1=\'8\' y1=\'2\' x2=\'8\' y2=\'6\'%3E%3C/line%3E%3Cline x1=\'3\' y1=\'10\' x2=\'21\' y2=\'10\'%3E%3C/line%3E%3C/svg%3E")');
        jQuery(dateRangeField).css('background-repeat', 'no-repeat');
        jQuery(dateRangeField).css('background-position', 'right 10px center');
        jQuery(dateRangeField).css('background-size', '16px');
        jQuery(dateRangeField).css('padding-right', '36px');
        
        // Configure the datepicker on show
        jQuery(dateRangeField).on('show.daterangepicker', function(ev, picker) {
            // Get the position of the input field
            var $input = jQuery(this);
            var inputPos = $input.offset();
            var inputHeight = $input.outerHeight();
            var inputWidth = $input.outerWidth();
            
            // Position the picker directly below the input
            var topPos = inputPos.top + inputHeight + 5;
            var leftPos = inputPos.left + (inputWidth / 2) - (320 / 2); // Center based on 320px width
            
            // Adjust if too close to edges
            var windowWidth = jQuery(window).width();
            if (leftPos < 10) {
                leftPos = 10;
            } else if (leftPos + 320 > windowWidth - 10) {
                leftPos = windowWidth - 320 - 10;
            }
            
            // Apply the position
            picker.container.css({
                top: topPos + 'px',
                left: leftPos + 'px',
                overflow: 'visible'
            });
            
            // Hide right calendar
            picker.container.find('.drp-calendar.right').hide();
            
            // Hide ranges completely
            picker.container.find('.ranges').hide();
            
            // Make left calendar take full width
            picker.container.find('.drp-calendar.left').css({
                'float': 'none',
                'width': '100%',
                'padding': '15px'
            });
            
            // Fix button layout
            picker.container.find('.drp-buttons').css({
                'display': 'flex',
                'justify-content': 'space-between',
                'align-items': 'center',
                'padding': '15px',
                'clear': 'both'
            });
        });
    }

    // Function to initialize country, state, city fields
    function initializeLocationFields() {
        // Check if fields exist
        if (!countryField) {
            console.log('Country field not found, retrying in 500ms');
            setTimeout(initializeLocationFields, 500);
            return;
        }
        
        // Set country to US
        jQuery(countryField).val('US');
        
        // Trigger change event using jQuery
        jQuery(countryField).trigger('change');
        
        // Check if state field populated after a delay
        setTimeout(function() {
            if (!stateField || stateField.options.length <= 1) {
                console.log('State field not populated with "US", trying "United States"');
                jQuery(countryField).val('United States').trigger('change');
                
                // Check again after another delay
                setTimeout(function() {
                    if (!stateField || stateField.options.length <= 1) {
                        console.log('Still not populated, trying with jQuery selector');
                        jQuery('select.country_auto').val('United States').trigger('change');
                    }
                }, 500);
            }
        }, 500);
    }
    
    // Initialize location fields
    initializeLocationFields();

    // Professional form initialization - load existing data from localStorage
    function initializeFormData() {
        console.log('Initializing form data from localStorage...');

        // Set default purpose to 'custom' if not already set
        if (!localStorage.getItem('selectedPurpose')) {
            localStorage.setItem('selectedPurpose', 'custom');
            console.log('Default purpose set to: custom');
        }

        // Load and set existing values
        const existingData = {
            purpose: localStorage.getItem('selectedPurpose') || 'custom',
            state: localStorage.getItem('selectedState'),
            city: localStorage.getItem('selectedCity'),
            location: localStorage.getItem('billboardLocation'),
            dates: localStorage.getItem('runDates')
        };

        console.log('Existing data found:', existingData);

        // Purpose is now always 'custom' - no need to set form value since it's hidden

        if (existingData.state) {
            setTimeout(() => {
                jQuery('select[name="state"], select.state_auto').val(existingData.state);
            }, 1000);
        }

        if (existingData.city) {
            setTimeout(() => {
                jQuery('select[name="city"], select.city_auto').val(existingData.city);
            }, 1500);
        }

        if (existingData.location) {
            setTimeout(() => {
                // First update billboard locations, then set the value
                updateBillboardLocations();
                setTimeout(() => {
                    jQuery('select[name="Billboard-Location"]').val(existingData.location);
                }, 200);
            }, 500);
        }

        if (existingData.dates) {
            jQuery('#date_range').val(existingData.dates);
        }

        // Run initial validation and update billboard locations
        setTimeout(() => {
            updateBillboardLocations();
            validateFormData();
        }, 2000);
    }

    // Initialize form data after a delay to ensure all fields are loaded
    setTimeout(initializeFormData, 1000);
    
    // Professional real-time data saving for all fields

    // Function to update billboard locations based on selected state and city
    function updateBillboardLocations() {
        const selectedState = jQuery('select.state_auto, select[name="state"]').val();
        const selectedCity = jQuery('select.city_auto, select[name="city"]').val();
        const billboardLocationSelect = jQuery('#billboard_location_field');

        console.log('Updating billboard locations for:', { state: selectedState, city: selectedCity });

        // Clear existing options
        billboardLocationSelect.empty();

        // Define billboard locations data
        const billboardLocations = {
            'Texas': {
                'Stephenville': [
                    {
                        value: 'Texas > Stephenville > 2095 West South loop, Stephenville, TX 76401',
                        text: 'Texas > Stephenville > 2095 West South loop, Stephenville, TX 76401'
                    }
                ]
            }
        };

        // Check if we have locations for the selected state and city
        if (selectedState && selectedCity &&
            billboardLocations[selectedState] &&
            billboardLocations[selectedState][selectedCity]) {

            // Add default option
            billboardLocationSelect.append('<option value="">Select a Billboard Location</option>');

            // Add available locations for this state/city combination
            const locations = billboardLocations[selectedState][selectedCity];
            locations.forEach(location => {
                billboardLocationSelect.append(`<option value="${location.value}">${location.text}</option>`);
            });

            console.log(`Added ${locations.length} billboard location(s) for ${selectedState} > ${selectedCity}`);

        } else {
            // No locations available or state/city not selected
            if (!selectedState || !selectedCity) {
                billboardLocationSelect.append('<option value="">Please select state and city first</option>');
            } else {
                billboardLocationSelect.append('<option value="">No billboard locations available for this area</option>');
            }
        }

        // Clear any previously selected billboard location since options changed
        localStorage.removeItem('billboardLocation');
    }

    // Add event listener for state field changes with immediate saving
    jQuery(document).on('change', 'select.state_auto, select[name="state"]', function() {
        const selectedState = jQuery(this).val();
        console.log('State changed to:', selectedState);

        if (selectedState) {
            localStorage.setItem('selectedState', selectedState);
            console.log('State saved to localStorage:', selectedState);
        }

        // Update billboard locations when state changes
        updateBillboardLocations();
        validateFormData();
    });

    // Add event listener for city field changes with immediate saving
    jQuery(document).on('change', 'select.city_auto, select[name="city"]', function() {
        const selectedCity = jQuery(this).val();
        console.log('City changed to:', selectedCity);

        if (selectedCity) {
            localStorage.setItem('selectedCity', selectedCity);
            console.log('City saved to localStorage:', selectedCity);
        }

        // Update billboard locations when city changes
        updateBillboardLocations();
        validateFormData();
    });

    // Add event listener for billboard location changes with immediate saving
    jQuery(document).on('change', 'select[name="Billboard-Location"]', function() {
        const billboardLocation = jQuery(this).val();
        console.log('Billboard location changed to:', billboardLocation);

        if (billboardLocation) {
            localStorage.setItem('billboardLocation', billboardLocation);
            console.log('Billboard location saved to localStorage:', billboardLocation);
        }
        validateFormData();
    });

    // Professional form validation function
    function validateFormData() {
        // Ensure purpose is always set to 'custom'
        if (!localStorage.getItem('selectedPurpose')) {
            localStorage.setItem('selectedPurpose', 'custom');
        }

        const requiredData = {
            purpose: localStorage.getItem('selectedPurpose') || 'custom',
            state: localStorage.getItem('selectedState'),
            city: localStorage.getItem('selectedCity'),
            location: localStorage.getItem('billboardLocation'),
            dates: localStorage.getItem('runDates')
        };

        const missingFields = [];
        Object.keys(requiredData).forEach(field => {
            const value = requiredData[field];
            // Purpose is always valid since it's set to 'custom'
            if (field === 'purpose') {
                return; // Skip validation for purpose
            }
            if (!value || value === 'Select a Purpose' || value === 'Not selected' || value.trim() === '') {
                missingFields.push(field);
            }
        });

        console.log('Form validation check:', {
            data: requiredData,
            missingFields: missingFields,
            isValid: missingFields.length === 0
        });

        // Update UI to show validation status (optional)
        updateValidationStatus(missingFields);

        return missingFields.length === 0;
    }

    // Update validation status in UI - Only for latest-dropdowns form
    function updateValidationStatus(missingFields) {
        console.log('updateValidationStatus: Checking validation status with missing fields:', missingFields);

        // Remove existing validation messages only from the latest-dropdowns context
        jQuery('.latest-dropdowns-validation').remove();

        // Find the specific form that contains our dropdown fields
        const targetForm = jQuery('#state_field, #city_field, #billboard_location_field').closest('.wpcf7-form');

        if (targetForm.length === 0) {
            console.log('updateValidationStatus: No latest-dropdowns form found, skipping UI update');
            return;
        }

        if (missingFields && missingFields.length > 0) {
            const message = `Please complete: ${missingFields.join(', ')}`;
            const statusDiv = jQuery(`<div class="validation-status latest-dropdowns-validation" style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 14px;">${message}</div>`);
            targetForm.prepend(statusDiv);
        } else {
            const statusDiv = jQuery(`<div class="validation-status latest-dropdowns-validation" style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 14px;">✅ All required fields completed!</div>`);
            targetForm.prepend(statusDiv);
        }

        // Update continue button if it exists
        const continueBtn = jQuery('.continue-btn, #continueBtn');
        if (continueBtn.length > 0) {
            const isValid = !missingFields || missingFields.length === 0;
            continueBtn.prop('disabled', !isValid);
            continueBtn.css('opacity', isValid ? '1' : '0.5');
        }

        console.log('updateValidationStatus: Validation status updated for latest-dropdowns context');
    }

    // Purpose is now set to 'custom' by default - no event listener needed
    // Send initial purpose message to Ad Design and Content form
    try {
        const defaultPurpose = 'custom';
        localStorage.setItem('selectedPurpose', defaultPurpose);

        // Send message to Ad Design and Content form if it's in another window/iframe
        const adDesignWindow = window.parent || window.opener || window;
        adDesignWindow.postMessage({
            type: 'purposeSelected',
            purpose: defaultPurpose
        }, window.location.origin);

        // Also try to send to all frames if this is in a parent window
        if (window.frames && window.frames.length > 0) {
            for (let i = 0; i < window.frames.length; i++) {
                try {
                    window.frames[i].postMessage({
                        type: 'purposeSelected',
                        purpose: defaultPurpose
                    }, window.location.origin);
                } catch (e) {
                    // Ignore cross-origin errors
                }
            }
        }

        // Also trigger a custom event on the current document
        const purposeEvent = new CustomEvent('purposeSelected', {
            detail: { purpose: defaultPurpose }
        });
        document.dispatchEvent(purposeEvent);

        console.log('Default purpose set and communicated:', defaultPurpose);
    } catch (e) {
        console.log('Could not send purpose message to other window:', e);
    }

    // Add form submission handler to store data for checkout
    jQuery(document).on('submit', '.wpcf7-form', function(e) {
        // Purpose is always 'custom' now
        const selectedPurpose = 'custom';
        const selectedState = jQuery('select[name="state"]').val();
        const selectedCity = jQuery('select[name="city"]').val();
        const billboardLocation = jQuery('select[name="Billboard-Location"]').val();
        const dateRange = jQuery('#date_range').val();

        // Store all form data for checkout - purpose is always 'custom'
        localStorage.setItem('selectedPurpose', selectedPurpose);
        if (selectedState) {
            localStorage.setItem('selectedState', selectedState);
        }
        if (selectedCity) {
            localStorage.setItem('selectedCity', selectedCity);
        }
        if (billboardLocation) {
            localStorage.setItem('billboardLocation', billboardLocation);
        }
        if (dateRange) {
            localStorage.setItem('runDates', dateRange);
        }

        console.log('Form data stored for checkout:', {
            purpose: selectedPurpose,
            state: selectedState,
            city: selectedCity,
            location: billboardLocation,
            dates: dateRange
        });
    });
});
</script>

<!-- Add daterangepicker library if not already included -->
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>