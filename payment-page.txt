<style>

.payment-title {
    text-align: center;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 30px;
    color: #333;
}
    
.payment-section {
    background: white;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-col {
    flex: 1;
}

.form-col label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-col input, .form-col select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.payment-method-section {
    margin-bottom: 20px;
}

.card-details {
    display: block;
    margin-top: 20px;
}

.order-total {
    background: #e9ecef;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
}

.total-amount {
    font-size: 24px;
    font-weight: bold;
    color: #0D7EE8;
}

.payment-buttons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.back-to-checkout-btn {
    flex: 1;
    background: #6c757d;
    color: white;
    padding: 15px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.back-to-checkout-btn:hover {
    background: #545b62;
    transform: translateY(-1px);
}

.submit-payment-btn {
    flex: 2;
    background: #28a745;
    color: white;
    padding: 15px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.submit-payment-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

.billboard-preview {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.billboard-preview img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.preview-placeholder {
    color: #6c757d;
    font-style: italic;
}

.preview-placeholder p {
    margin: 0;
    font-size: 16px;
}


</style>

<h2 class="payment-title">💳 Payment Information</h2>
    
    <!-- Order Total Display -->
    <div class="order-total">
        <div>Total Amount:</div>
        <div class="total-amount" id="totalAmount">$0.00</div>
        <div style="font-size: 14px; color: #666; margin-top: 5px;">
            <span id="orderSummary">Loading order details...</span>
        </div>
    </div>

    <!-- Billboard Preview -->
    <div class="payment-section">
        <h3>🎨 Your Billboard Design</h3>
        <div class="billboard-preview" id="billboardPreview">
            <div class="preview-placeholder">
                <p>Loading your billboard design...</p>
            </div>
        </div>
    </div>

    <!-- Customer Information -->
    <div class="payment-section">
        <h3>📋 Customer Information</h3>
        <div class="form-row">
            <div class="form-col">
                <label>First Name*</label>
                [text* first_name placeholder "John"]
            </div>
            <div class="form-col">
                <label>Last Name*</label>
                [text* last_name placeholder "Doe"]
            </div>
        </div>
        <div class="form-row">
            <div class="form-col">
                <label>Email Address*</label>
                [email* customer_email placeholder "<EMAIL>"]
            </div>
            <div class="form-col">
                <label>Phone Number*</label>
                [tel* phone_number placeholder "(*************"]
            </div>
        </div>
    </div>
    
    <!-- Credit Card Payment -->
    <div class="payment-section">
        <h3>💳 Credit/Debit Card Information</h3>
        <div class="payment-method-section">
            <!-- Credit Card Section -->
            <div class="card-details" id="cardDetails">
                <div class="form-row">
                    <div class="form-col">
                        <label>Card Number*</label>
                        [text* card_number placeholder "1234 5678 9012 3456"]
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-col">
                        <label>Expiry Month*</label>
                        [select* expiry_month "Select Month" "01" "02" "03" "04" "05" "06" "07" "08" "09" "10" "11" "12"]
                    </div>
                    <div class="form-col">
                        <label>Expiry Year*</label>
                        [select* expiry_year "Select Year" "2024" "2025" "2026" "2027" "2028" "2029" "2030" "2031" "2032"]
                    </div>
                    <div class="form-col">
                        <label>CVV*</label>
                        [text* cvv placeholder "123"]
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-col">
                        <label>Cardholder Name*</label>
                        [text* cardholder_name placeholder "John Doe"]
                    </div>
                </div>
                <div style="margin-top: 15px; padding: 15px; background: #e8f5e8; border: 1px solid #c3e6c3; border-radius: 6px;">
                    <div style="display: flex; align-items: center; gap: 10px; color: #155724;">
                        <span style="font-size: 18px;">🔒</span>
                        <span style="font-weight: 600; font-size: 14px;">Secure Payment Processing</span>
                    </div>
                    <p style="margin: 8px 0 0 0; font-size: 13px; color: #155724;">
                        Your payment information is encrypted and processed securely. We accept Visa, MasterCard, American Express, and Discover.
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Hidden fields to store order data -->
    [hidden payment_method id:payment_method value:"card"]
    [hidden order_total id:order_total]
    [hidden order_details id:order_details]

    <!-- Location and booking data -->
    [hidden country id:hidden_country]
    [hidden state id:hidden_state]
    [hidden city id:hidden_city]
    [hidden billboard_location id:payment_location]
    [hidden run_dates id:payment_dates]
    [hidden purpose id:payment_purpose]

    <!-- Billboard design data -->
    [hidden ad_design_data id:payment_design]
    [file billboard_image id:billboard_file accept:image/* class:hidden-file-input]
    [hidden billboard_image_name id:billboard_name]

    <!-- Additional design metadata -->
    [hidden selected_template id:selected_template]
    [hidden text_lines id:text_lines]
    [hidden background_color id:bg_color]
    [hidden background_type id:bg_type]
    
    <!-- Action Buttons -->
    <div class="payment-buttons">
        <button type="button" class="back-to-checkout-btn" onclick="goBackToCheckout()">← Back to Checkout</button>
        [submit class:submit-payment-btn "🔒 Process Payment"]
    </div>

<style>
/* Hide the file input since we'll populate it programmatically */
.hidden-file-input {
    display: none !important;
}
</style>

<script>
// Credit card form validation
function validateCreditCardForm() {
    const requiredFields = [
        { name: 'card_number', label: 'Card Number' },
        { name: 'expiry_month', label: 'Expiry Month' },
        { name: 'expiry_year', label: 'Expiry Year' },
        { name: 'cvv', label: 'CVV' },
        { name: 'cardholder_name', label: 'Cardholder Name' }
    ];

    for (const field of requiredFields) {
        const element = document.querySelector(`[name="${field.name}"]`);
        if (!element || !element.value || element.value === 'Select Month' || element.value === 'Select Year') {
            alert(`Please fill in the ${field.label} field.`);
            if (element) element.focus();
            return false;
        }
    }

    // Basic card number validation (remove spaces and check length)
    const cardNumber = document.querySelector('[name="card_number"]').value.replace(/\s/g, '');
    if (cardNumber.length < 13 || cardNumber.length > 19) {
        alert('Please enter a valid card number.');
        return false;
    }

    // CVV validation
    const cvv = document.querySelector('[name="cvv"]').value;
    if (cvv.length < 3 || cvv.length > 4) {
        alert('Please enter a valid CVV (3-4 digits).');
        return false;
    }

    return true;
}

// Initialize payment page
document.addEventListener('DOMContentLoaded', function() {
    // Restore payment form data if returning from checkout
    restorePaymentFormData();

    // Load order data from localStorage
    const orderData = {
        total: localStorage.getItem('totalCost') || '0.00',
        location: localStorage.getItem('billboardLocation') || 'Not selected',
        dates: localStorage.getItem('runDates') || 'Not selected',
        purpose: localStorage.getItem('selectedPurpose') || 'Not selected',
        design: localStorage.getItem('adDesignData') || '{}'
    };
    
    // Update display
    document.getElementById('totalAmount').textContent = `$${orderData.total}`;
    document.getElementById('orderSummary').textContent = 
        `${orderData.purpose} billboard in ${orderData.location} for ${orderData.dates}`;
    
    // Set hidden fields
    document.getElementById('order_total').value = orderData.total;
    document.getElementById('order_details').value = JSON.stringify(orderData);
    document.getElementById('payment_location').value = orderData.location;
    document.getElementById('payment_dates').value = orderData.dates;
    document.getElementById('payment_purpose').value = orderData.purpose;
    document.getElementById('payment_design').value = orderData.design;

    // Populate all additional hidden fields including billboard image file
    populateAllHiddenFields();

    // Display billboard preview
    displayBillboardPreview();
});

// Function to convert base64 to File object
function base64ToFile(base64String, filename) {
    // Remove data URL prefix if present
    const base64Data = base64String.replace(/^data:image\/[a-z]+;base64,/, '');

    // Convert base64 to binary
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    return new File([byteArray], filename, { type: 'image/png' });
}

// Comprehensive function to populate all hidden fields with data from localStorage
function populateAllHiddenFields() {
    console.log('Populating all hidden fields with localStorage data...');

    // Location and booking data from latest-dropdowns.txt
    const country = localStorage.getItem('selectedCountry') || '';
    const state = localStorage.getItem('selectedState') || '';
    const city = localStorage.getItem('selectedCity') || '';
    const billboardLocation = localStorage.getItem('billboardLocation') || '';
    const runDates = localStorage.getItem('runDates') || '';
    const purpose = localStorage.getItem('selectedPurpose') || '';

    // Billboard design data from CF7-billboard-cutom-CF7.txt
    const adDesignData = localStorage.getItem('adDesignData') || '';
    const billboardCanvasImage = localStorage.getItem('billboardCanvasImage') || '';

    // Additional design metadata
    const selectedTemplate = localStorage.getItem('currentTemplate') || localStorage.getItem('selectedTemplate') || '';
    const textLines = localStorage.getItem('billboardTextLines') || '';
    const bgColor = localStorage.getItem('selectedBackgroundColor') || '';
    const bgType = localStorage.getItem('selectedBackgroundType') || '';

    // Safely set hidden field values with error handling
    const setHiddenField = (id, value) => {
        const element = document.getElementById(id);
        if (element) {
            element.value = value;
            console.log(`Set ${id}:`, value ? 'Present' : 'Empty');
        } else {
            console.warn(`Hidden field ${id} not found`);
        }
    };

    // Set all hidden fields
    setHiddenField('hidden_country', country);
    setHiddenField('hidden_state', state);
    setHiddenField('hidden_city', city);
    setHiddenField('payment_location', billboardLocation);
    setHiddenField('payment_dates', runDates);
    setHiddenField('payment_purpose', purpose);
    setHiddenField('payment_design', adDesignData);
    setHiddenField('selected_template', selectedTemplate);
    setHiddenField('text_lines', textLines);
    setHiddenField('bg_color', bgColor);
    setHiddenField('bg_type', bgType);

    // Convert base64 billboard image to actual file for proper submission
    if (billboardCanvasImage) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `billboard-${purpose}-${timestamp}.png`;
            const imageFile = base64ToFile(billboardCanvasImage, filename);

            // Create a DataTransfer object to set the file input
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(imageFile);

            const fileInput = document.getElementById('billboard_file');
            if (fileInput) {
                fileInput.files = dataTransfer.files;
                setHiddenField('billboard_name', filename);
                console.log('Billboard image converted to file:', filename);
            }
        } catch (error) {
            console.error('Error converting billboard image to file:', error);
        }
    }

    console.log('All hidden fields populated successfully');
}

// Display billboard preview image
function displayBillboardPreview() {
    console.log('Displaying billboard preview...');

    const previewContainer = document.getElementById('billboardPreview');
    const billboardImage = localStorage.getItem('billboardCanvasImage') || localStorage.getItem('adPreviewImage');

    if (billboardImage && previewContainer) {
        try {
            // Create image element
            const img = document.createElement('img');
            img.src = billboardImage;
            img.alt = 'Your Billboard Design';
            img.style.maxWidth = '100%';
            img.style.maxHeight = '300px';
            img.style.borderRadius = '4px';
            img.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';

            // Clear placeholder and add image
            previewContainer.innerHTML = '';
            previewContainer.appendChild(img);

            console.log('Billboard preview image displayed successfully');
        } catch (error) {
            console.error('Error displaying billboard preview:', error);
            previewContainer.innerHTML = '<div class="preview-placeholder"><p>Unable to load billboard preview</p></div>';
        }
    } else {
        console.warn('No billboard image found in localStorage');
        if (previewContainer) {
            previewContainer.innerHTML = '<div class="preview-placeholder"><p>No billboard design found. Please go back and create your design.</p></div>';
        }
    }
}

// Back to checkout function
function goBackToCheckout() {
    console.log('Navigating back to checkout page...');

    // Preserve payment form data in case user returns
    const paymentFormData = {
        firstName: document.querySelector('input[name="first_name"]').value,
        lastName: document.querySelector('input[name="last_name"]').value,
        email: document.querySelector('input[name="customer_email"]').value,
        phone: document.querySelector('input[name="phone_number"]').value,
        cardNumber: document.querySelector('input[name="card_number"]').value,
        cardholderName: document.querySelector('input[name="cardholder_name"]').value,
        paymentMethod: 'card'
    };

    // Store payment form data
    localStorage.setItem('paymentFormData', JSON.stringify(paymentFormData));

    // Navigate back to checkout
    window.location.href = 'https://www.borgesmedia.com/custom-billboard-ad/';
}

// Restore payment form data if returning from checkout
function restorePaymentFormData() {
    const savedData = localStorage.getItem('paymentFormData');
    if (savedData) {
        try {
            const formData = JSON.parse(savedData);

            // Restore form fields
            if (formData.firstName) document.querySelector('input[name="first_name"]').value = formData.firstName;
            if (formData.lastName) document.querySelector('input[name="last_name"]').value = formData.lastName;
            if (formData.email) document.querySelector('input[name="customer_email"]').value = formData.email;
            if (formData.phone) document.querySelector('input[name="phone_number"]').value = formData.phone;
            if (formData.cardNumber) document.querySelector('input[name="card_number"]').value = formData.cardNumber;
            if (formData.cardholderName) document.querySelector('input[name="cardholder_name"]').value = formData.cardholderName;

            console.log('Payment form data restored:', formData);
        } catch (e) {
            console.error('Error restoring payment form data:', e);
        }
    }
}

// Handle form submission
document.addEventListener('wpcf7mailsent', function(event) {
    console.log('Payment form submitted successfully');

    // Get form data
    const formData = {
        firstName: document.querySelector('input[name="first_name"]').value,
        lastName: document.querySelector('input[name="last_name"]').value,
        email: document.querySelector('input[name="customer_email"]').value,
        phone: document.querySelector('input[name="phone_number"]').value,
        cardNumber: document.querySelector('input[name="card_number"]').value,
        expiryMonth: document.querySelector('select[name="expiry_month"]').value,
        expiryYear: document.querySelector('select[name="expiry_year"]').value,
        cvv: document.querySelector('input[name="cvv"]').value,
        cardholderName: document.querySelector('input[name="cardholder_name"]').value,
        amount: document.getElementById('order_total').value,
        orderDetails: document.getElementById('order_details').value
    };

    // Clear stored payment data
    localStorage.removeItem('paymentFormData');

    // Show success message
    alert(`Thank you ${formData.firstName}! Your payment information has been submitted. You will receive a confirmation email shortly.`);

    // Redirect to success page
    window.location.href = 'https://www.borgesmedia.com/billboard-ad-pay-success/';
});

// Add form validation before submission
document.addEventListener('wpcf7beforesubmit', function(event) {
    if (!validateCreditCardForm()) {
        event.preventDefault();
        return false;
    }
});
</script>